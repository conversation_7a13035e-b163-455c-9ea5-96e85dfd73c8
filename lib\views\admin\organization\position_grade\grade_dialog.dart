import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 职级创建弹窗
class GradeDialog extends StatefulWidget {
  const GradeDialog({super.key});

   @override
  State<GradeDialog> createState() => _GradeDialogState();
}

class _GradeDialogState extends State<GradeDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();


  /// 重置数据
  void resetFormData() {
    
  }

  /// 提交
  createRequest(BuildContext context){

  }

  /// 打开添加部门弹窗
  void _showGradeDialog(BuildContext context) {
    double labelWidth = 80;

    /// 间距
    double spacing = 20;

    AppDialog.show(
      width: 480,
      context: context,
      title: '添加部门',
      isDrawer: true,
      slideDirection: SlideDirection.right,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          return Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppInput(
                  label: "名称",
                  labelWidth: labelWidth,
                  labelPosition: LabelPosition.left,
                  hintText: "名称",
                  size: InputSize.medium,
                  controller: _nameController,
                  maxLength: 30,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return '请输入名称';
                    }
                    return null;
                  },
                  onChanged: (value) {},
                ),
              ],
            ),
          );
        },
      ),
      footer: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          StatefulBuilder(
            builder: (context, setState) {
              return Checkbox(
                value: isAddNext,
                onChanged: (value) {
                  setState(() {
                    isAddNext = !isAddNext;
                  });
                },
              );
            },
          ),
          Text('继续新建下一条'),
          const SizedBox(width: 10),
          AppButton(text: '取消', type: ButtonType.default_, onPressed: () => context.pop()),
          const SizedBox(width: 10),
          AppButton(text: '确定', type: ButtonType.primary, onPressed: () => createRequest(context)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AppButton(
      text: '添加部门',
      type: ButtonType.primary,
      loading: btnLoading,
      onPressed: () {
        _showGradeDialog(context);
      },
    );
  }
}
